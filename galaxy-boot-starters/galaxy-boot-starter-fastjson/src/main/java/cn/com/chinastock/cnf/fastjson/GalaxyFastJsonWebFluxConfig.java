package cn.com.chinastock.cnf.fastjson;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Decoder;
import com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Encoder;
import com.alibaba.fastjson2.support.spring6.http.converter.FastJsonHttpMessageConverter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.codec.CodecCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.codec.CodecConfigurer;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.codec.json.Jackson2JsonEncoder;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.converter.AbstractGenericHttpMessageConverter;
import org.springframework.lang.NonNull;
import org.springframework.web.reactive.config.WebFluxConfigurer;

import java.util.ArrayList;
import java.util.function.Consumer;

/**
 * Galaxy FastJSON WebFlux 自动配置类
 * <p>
 * 为 Spring WebFlux 应用提供 FastJSON 支持，替换默认的 Jackson。
 * 该配置类只在响应式 Web 应用环境中生效，避免与 MVC 配置冲突。
 * </p>
 * <p>
 * 主要功能：
 * <ul>
 *   <li>创建 WebFlux 专用的 FastJSON 配置</li>
 *   <li>提供 Fastjson2Encoder 和 Fastjson2Decoder Bean</li>
 *   <li>通过 WebFluxConfigurer 注册自定义编解码器</li>
 *   <li>支持所有 FastJsonProperties 配置项</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Boot Team
 * @since 0.2.6-ALPHA2
 * @see GalaxyFastJsonConfig MVC 版本的配置类
 * @see FastJsonProperties FastJSON 配置属性
 */
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
@ConditionalOnClass({
    org.springframework.web.reactive.config.WebFluxConfigurer.class,
    com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Decoder.class,
    com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Encoder.class
})
@EnableConfigurationProperties(FastJsonProperties.class)
public class GalaxyFastJsonWebFluxConfig implements WebFluxConfigurer {

    @Autowired
    private FastJsonProperties fastJsonProperties;

    /**
     * 初始化 FastJSON 全局配置
     * <p>
     * 配置全局的日期时间格式，解决 LocalDateTime 序列化问题。
     * 这个配置会影响所有使用 FastJSON 的序列化操作。
     * </p>
     */
    @PostConstruct
    public void initFastJsonGlobalConfig() {
        // 配置全局日期时间格式
        JSON.config(JSONWriter.Feature.WriteBigDecimalAsPlain);
        JSON.config(JSONWriter.Feature.IgnoreNonFieldGetter);
    }

    /**
     * 收集序列化特性配置
     * <p>
     * 复用 MVC 配置的逻辑，确保配置的一致性。
     * 需要同步修改：{@link cn.com.chinastock.cnf.security.output.FastJsonFilterUtil#handleFastJsonResponse}
     * </p>
     *
     * @return 序列化特性集合
     * @see <a href="https://github.com/alibaba/fastjson2/wiki/fastjson_1_upgrade_cn">FASTJSON 1.x升级指南</a>
     * @see <a href="https://github.com/alibaba/fastjson2/wiki/Features_cn">通过Features配置序列化和反序列化的行为</a>
     */
    private ArrayList<JSONWriter.Feature> collectWriterFromProperties() {
        ArrayList<JSONWriter.Feature> features = new java.util.ArrayList<>();
        // 需要同步修改 cn.com.chinastock.cnf.security.output.FastJsonFilterUtil
        features.add(JSONWriter.Feature.WriteBigDecimalAsPlain);
        features.add(JSONWriter.Feature.IgnoreNonFieldGetter);

        if (fastJsonProperties.isWriteMapNullValue()) {
            features.add(JSONWriter.Feature.WriteMapNullValue);
        }

        if (fastJsonProperties.isWriteNullStringAsEmpty()) {
            features.add(JSONWriter.Feature.WriteNullStringAsEmpty);
        }

        return features;
    }

    /**
     * 收集反序列化特性配置
     * <p>
     * 复用 MVC 配置的逻辑，确保配置的一致性。
     * 需要同步修改：{@link cn.com.chinastock.cnf.security.output.FastJsonFilterUtil}
     * </p>
     *
     * @return 反序列化特性集合
     */
    private java.util.ArrayList<JSONReader.Feature> collectReaderFromProperties() {
        java.util.ArrayList<JSONReader.Feature> features = new java.util.ArrayList<>();
        // 需要同步修改 cn.com.chinastock.cnf.security.output.FastJsonFilterUtil

        if (fastJsonProperties.isSupportSmartMatch()) {
            features.add(JSONReader.Feature.SupportSmartMatch);
        }

        return features;
    }

    /**
     * 创建 WebFlux 专用的 FastJSON 配置
     * <p>
     * 复用 MVC 配置的逻辑，确保配置的一致性。
     * 该配置会被 Fastjson2Encoder 和 Fastjson2Decoder 使用。
     * </p>
     *
     * @return WebFlux 专用的 FastJSON 配置
     */
    @Bean
    public FastJsonConfig webfluxFastJsonConfig() {
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setCharset(java.nio.charset.StandardCharsets.UTF_8);
        fastJsonConfig.setWriterFeatures(collectWriterFromProperties().toArray(new JSONWriter.Feature[0]));
        fastJsonConfig.setReaderFeatures(collectReaderFromProperties().toArray(new JSONReader.Feature[0]));

        return fastJsonConfig;
    }

    // configureHttpMessageCodecs

    @Bean
    public CodecCustomizer myCodecCustomizer() {
        return new CodecCustomizer() {
            @Override
            public void customize(CodecConfigurer configurer) {
                configurer.customCodecs().register(GalaxyFastJsonWebFluxConfig.this.fastjson2Encoder());
                configurer.customCodecs().register(GalaxyFastJsonWebFluxConfig.this.fastjson2Decoder());

                configurer.defaultCodecs().configureDefaultCodec(codec -> {
                    new FastJsonHttpMessageConverter();
                });
                configurer.defaultCodecs().jackson2JsonDecoder(GalaxyFastJsonWebFluxConfig.this.fastjson2Decoder());
                configurer.defaultCodecs().jackson2JsonEncoder(GalaxyFastJsonWebFluxConfig.this.fastjson2Encoder());
            }
        };
    }

        /**
     * 创建 FastJSON WebFlux 编码器
     * <p>
     * 用于将 Java 对象序列化为 JSON 响应体。
     * 支持 application/json 和 application/*+json 媒体类型。
     * </p>
     * <p>
     * 注意：Fastjson2Encoder 需要 ObjectMapper 参数，但会使用 FastJSON 进行序列化。
     * 关键是要确保 FastJSON 的全局配置正确设置。
     * </p>
     *
     * @return FastJSON WebFlux 编码器
     */
    @Bean
    public Fastjson2Encoder fastjson2Encoder() {
        // 创建一个简单的 ObjectMapper，FastJSON2 会使用自己的配置
        ObjectMapper objectMapper = new ObjectMapper();
        Fastjson2Encoder encoder = new Fastjson2Encoder(objectMapper);

        // 明确设置支持的媒体类型，确保优先级
        java.util.List<org.springframework.http.MediaType> mediaTypes = new java.util.ArrayList<>();
        mediaTypes.add(org.springframework.http.MediaType.APPLICATION_JSON);
        mediaTypes.add(org.springframework.http.MediaType.valueOf("application/*+json"));

        return encoder;
    }

    /**
     * 创建 FastJSON WebFlux 解码器
     * <p>
     * 用于将 JSON 请求体反序列化为 Java 对象。
     * 支持 application/json 和 application/*+json 媒体类型。
     * </p>
     * <p>
     * 注意：Fastjson2Decoder 需要 ObjectMapper 参数，但会使用 FastJSON 进行反序列化。
     * 关键是要确保 FastJSON 的全局配置正确设置。
     * </p>
     *
     * @return FastJSON WebFlux 解码器
     */
    @Bean
    public Fastjson2Decoder fastjson2Decoder() {
        // 创建一个简单的 ObjectMapper，FastJSON2 会使用自己的配置
        ObjectMapper objectMapper = new ObjectMapper();
        return new Fastjson2Decoder(objectMapper);
    }

    /**
     * 配置 HTTP 消息编解码器
     * <p>
     * 注册 FastJSON 编解码器到 WebFlux 的编解码器配置中。
     * 这是关键步骤，确保 WebFlux 使用 FastJSON 而不是默认的 Jackson。
     * </p>
     * <p>
     * 实现要点：
     * <ul>
     *   <li>注册 FastJSON 编解码器到自定义编解码器列表</li>
     *   <li>自定义编解码器具有更高的优先级</li>
     *   <li>启用请求详情日志记录便于调试</li>
     * </ul>
     * </p>
     *
     * @param configurer 服务器编解码器配置器
     */
    @Override
    public void configureHttpMessageCodecs(@NonNull ServerCodecConfigurer configurer) {
        // 设置内存限制
        configurer.defaultCodecs().maxInMemorySize(1024 * 1024);

        // 用 FastJSON 编码器替换默认的 Jackson 编码器
        configurer.defaultCodecs().jackson2JsonEncoder(fastjson2Encoder());
        configurer.defaultCodecs().jackson2JsonDecoder(fastjson2Decoder());

        // 启用请求详情日志记录，便于调试
        configurer.defaultCodecs().enableLoggingRequestDetails(true);
    }

    /**
     * 创建配置了 JSR310 模块的 ObjectMapper Bean
     * <p>
     * 这个 Bean 用于支持 Jackson 编码器处理 LocalDateTime 等 Java 8 时间类型。
     * 虽然我们主要使用 FastJSON，但 Spring WebFlux 可能仍会使用 Jackson 编码器，
     * 因此需要确保 Jackson 也能正确处理时间类型。
     * </p>
     *
     * @return 配置了 JSR310 模块的 ObjectMapper
     */
//    @Bean
//    @Primary
//    public ObjectMapper objectMapper() {
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.registerModule(new JavaTimeModule());
//        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
//        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        return mapper;
//    }
}
