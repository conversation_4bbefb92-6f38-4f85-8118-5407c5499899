# Galaxy FastJSON WebFlux 使用指南

## 概述

`galaxy-boot-starter-fastjson` 现在完全支持 Spring WebFlux，可以在响应式应用中无缝替换 Jackson 进行 JSON 处理。

## 功能特性

- ✅ **自动配置**：在 WebFlux 环境中自动启用 FastJSON 支持
- ✅ **条件配置**：智能检测应用类型，避免与 MVC 配置冲突
- ✅ **配置复用**：WebFlux 配置复用 MVC 的 FastJsonProperties 配置
- ✅ **完整支持**：支持所有 FastJSON 序列化/反序列化特性

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-fastjson</artifactId>
    <version>0.2.6-ALPHA2</version>
</dependency>

<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-webflux</artifactId>
</dependency>
```

### 2. 创建 WebFlux 应用

```java
@SpringBootApplication
@EnableWebFlux
public class WebFluxApplication {
    public static void main(String[] args) {
        SpringApplication.run(WebFluxApplication.class, args);
    }
}
```

### 3. 创建响应式控制器

```java
@RestController
public class UserController {
    
    @GetMapping("/users/{id}")
    public Mono<User> getUser(@PathVariable Long id) {
        // FastJSON 会自动处理序列化
        return userService.findById(id);
    }
    
    @PostMapping("/users")
    public Mono<User> createUser(@RequestBody User user) {
        // FastJSON 会自动处理反序列化
        return userService.save(user);
    }
    
    @GetMapping("/users")
    public Flux<User> getAllUsers() {
        // 支持流式响应
        return userService.findAll();
    }
}
```

### 4. 函数式路由支持

```java
@Configuration
public class RouterConfig {
    
    @Bean
    public RouterFunction<ServerResponse> routes(UserService userService) {
        return RouterFunctions.route()
            .GET("/api/users/{id}", request -> {
                Long id = Long.valueOf(request.pathVariable("id"));
                return userService.findById(id)
                    .flatMap(user -> ServerResponse.ok().bodyValue(user))
                    .switchIfEmpty(ServerResponse.notFound().build());
            })
            .POST("/api/users", request -> 
                request.bodyToMono(User.class)
                    .flatMap(userService::save)
                    .flatMap(user -> ServerResponse.ok().bodyValue(user))
            )
            .build();
    }
}
```

## 配置选项

### application.properties

```properties
# FastJSON 配置（与 MVC 共享）
galaxy.fastjson.writeMapNullValue=true
galaxy.fastjson.writeNullStringAsEmpty=true
galaxy.fastjson.supportSmartMatch=true
```

### application.yml

```yaml
galaxy:
  fastjson:
    writeMapNullValue: true
    writeNullStringAsEmpty: true
    supportSmartMatch: true
```

## 技术实现

### 核心组件

1. **GalaxyFastJsonWebFluxConfig**：WebFlux 自动配置类
2. **Fastjson2Encoder**：响应体序列化编码器
3. **Fastjson2Decoder**：请求体反序列化解码器
4. **FastJsonConfig**：FastJSON 配置对象

### 条件配置

```java
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
@ConditionalOnClass({
    WebFluxConfigurer.class,
    Fastjson2Decoder.class,
    Fastjson2Encoder.class
})
public class GalaxyFastJsonWebFluxConfig implements WebFluxConfigurer {
    // 配置实现
}
```

### 编解码器注册

```java
@Override
public void configureHttpMessageCodecs(ServerCodecConfigurer configurer) {
    // 启用请求详情日志记录
    configurer.defaultCodecs().enableLoggingRequestDetails(true);
    
    // 注册 FastJSON 编解码器
    configurer.customCodecs().register(fastjson2Encoder());
    configurer.customCodecs().register(fastjson2Decoder());
}
```

## 测试验证

项目包含完整的测试套件：

- **单元测试**：验证配置类和 Bean 创建
- **集成测试**：验证在真实 WebFlux 环境中的功能
- **功能测试**：验证 JSON 序列化/反序列化的正确性

运行测试：

```bash
mvn test
```

## 注意事项

1. **自动检测**：配置会自动检测应用类型，在 WebFlux 环境中启用
2. **配置隔离**：WebFlux 和 MVC 配置相互独立，避免冲突
3. **性能优势**：FastJSON 在序列化性能上通常优于 Jackson
4. **兼容性**：完全兼容 Spring Boot 3.x 和 Spring 6.x

## 故障排除

### 常见问题

1. **配置未生效**：检查是否正确添加了 WebFlux 依赖
2. **Bean 冲突**：确保没有手动配置冲突的编解码器
3. **序列化问题**：检查 FastJsonProperties 配置是否正确

### 调试技巧

启用详细日志：

```properties
logging.level.org.springframework.web.reactive=DEBUG
logging.level.cn.com.chinastock.cnf.fastjson=DEBUG
```

## 更多信息

- [FastJSON2 官方文档](https://github.com/alibaba/fastjson2)
- [Spring WebFlux 文档](https://docs.spring.io/spring-framework/docs/current/reference/html/web-reactive.html)
- [Galaxy Boot 框架文档](../../../README.md)
